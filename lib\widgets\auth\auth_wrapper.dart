import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:firebase_core/firebase_core.dart';
import '../../providers/simple_auth_provider.dart';
import '../../screens/auth/simple_login_screen.dart';
import '../../main.dart';
import '../../config/firebase_config.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _ensureFirebaseInitialized(),
      builder: (context, snapshot) {
        // إذا كان Firebase لا يزال يتم تهيئته
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري تهيئة التطبيق...'),
                ],
              ),
            ),
          );
        }

        // إذا حدث خطأ في تهيئة Firebase
        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('خطأ في تهيئة التطبيق: ${snapshot.error}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      // إعادة تحميل الصفحة
                      Navigator.of(context).pushReplacementNamed('/');
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            ),
          );
        }

        // Firebase تم تهيئته بنجاح، اعرض AuthProvider
        return Consumer<SimpleAuthProvider>(
          builder: (context, authProvider, child) {
            // إذا لم يتم تهيئة المصادقة بعد، اعرض شاشة تحميل
            if (!authProvider.isInitialized) {
              return const Scaffold(
                body: Center(child: CircularProgressIndicator()),
              );
            }

            // إذا كان المستخدم مسجل دخول، اعرض الشاشة الرئيسية
            if (authProvider.isLoggedIn) {
              return const MainScreen();
            }

            // إذا لم يكن مسجل دخول، اعرض شاشة تسجيل الدخول
            return const SimpleLoginScreen();
          },
        );
      },
    );
  }

  Future<void> _ensureFirebaseInitialized() async {
    try {
      // التحقق من أن Firebase تم تهيئته
      if (Firebase.apps.isEmpty) {
        await FirebaseConfig.initialize();
      }
    } catch (e) {
      throw Exception('فشل في تهيئة Firebase: $e');
    }
  }
}
