import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../providers/simple_auth_provider.dart';
import '../providers/theme_provider.dart';
import '../screens/auth/simple_login_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    final authProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
    final user = authProvider.firebaseUser;
    
    if (user != null) {
      _nameController.text = user.displayName ?? '';
      _emailController.text = user.email ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, SimpleAuthProvider>(
      builder: (context, themeProvider, authProvider, child) {
        final user = authProvider.firebaseUser;
        final isDark = themeProvider.isDarkMode;
        
        return Scaffold(
          backgroundColor: isDark ? const Color(0xFF0F172A) : Colors.grey[50],
          body: CustomScrollView(
            slivers: [
              // App Bar with Profile Header
              SliverAppBar(
                expandedHeight: 280,
                floating: false,
                pinned: true,
                backgroundColor: const Color(0xFF6366F1),
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFF6366F1),
                          Color(0xFF8B5CF6),
                          Color(0xFFEC4899),
                        ],
                      ),
                    ),
                    child: Stack(
                      children: [
                        // Background Pattern
                        Positioned.fill(
                          child: Opacity(
                            opacity: 0.1,
                            child: Container(
                              decoration: const BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage('assets/pattern.png'),
                                  repeat: ImageRepeat.repeat,
                                ),
                              ),
                            ),
                          ),
                        ),
                        // Profile Content
                        Positioned(
                          top: 40,
                          left: 24,
                          right: 24,
                          child: Column(
                            children: [
                              // Profile Avatar
                              Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.15),
                                  borderRadius: BorderRadius.circular(60),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 3,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(alpha: 0.1),
                                      blurRadius: 20,
                                      offset: const Offset(0, 8),
                                    ),
                                  ],
                                ),
                                child: user?.photoURL != null
                                    ? ClipRRect(
                                        borderRadius: BorderRadius.circular(57),
                                        child: Image.network(
                                          user!.photoURL!,
                                          fit: BoxFit.cover,
                                          errorBuilder: (context, error, stackTrace) {
                                            return const Icon(
                                              Icons.person_rounded,
                                              size: 60,
                                              color: Colors.white,
                                            );
                                          },
                                        ),
                                      )
                                    : const Icon(
                                        Icons.person_rounded,
                                        size: 60,
                                        color: Colors.white,
                                      ),
                              ),
                              const SizedBox(height: 20),
                              // Name
                              Text(
                                user?.displayName ?? 'مستخدم',
                                style: GoogleFonts.cairo(
                                  fontSize: 28,
                                  fontWeight: FontWeight.w800,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Email
                              Text(
                                user?.email ?? 'لا يوجد إيميل',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                              const SizedBox(height: 8),
                              // User Type Badge
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Text(
                                  user?.isAnonymous == true ? 'ضيف' : 'مستخدم مسجل',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Settings List
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      // Edit Profile Section
                      if (user?.isAnonymous != true) ...[
                        _buildSettingItem(
                          icon: Icons.edit,
                          title: 'تعديل الملف الشخصي',
                          onTap: _showEditProfileDialog,
                        ),
                        const SizedBox(height: 12),
                      ],
                      
                      // Account Info Section
                      _buildSectionHeader('معلومات الحساب'),
                      _buildInfoItem(
                        icon: Icons.email_outlined,
                        title: 'البريد الإلكتروني',
                        value: user?.email ?? 'غير محدد',
                      ),
                      _buildInfoItem(
                        icon: Icons.verified_user_outlined,
                        title: 'حالة التحقق',
                        value: user?.emailVerified == true ? 'محقق' : 'غير محقق',
                        valueColor: user?.emailVerified == true ? Colors.green : Colors.orange,
                      ),
                      _buildInfoItem(
                        icon: Icons.access_time_outlined,
                        title: 'تاريخ الإنشاء',
                        value: user?.metadata.creationTime != null
                            ? _formatDate(user!.metadata.creationTime!)
                            : 'غير محدد',
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // App Settings Section
                      _buildSectionHeader('إعدادات التطبيق'),
                      Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return _buildSettingItem(
                            icon: themeProvider.isDarkMode
                                ? Icons.dark_mode
                                : Icons.light_mode,
                            title: 'الوضع المظلم',
                            trailing: Switch(
                              value: themeProvider.isDarkMode,
                              onChanged: (value) {
                                themeProvider.toggleTheme();
                              },
                              activeColor: const Color(0xFF6366F1),
                            ),
                            onTap: () {
                              themeProvider.toggleTheme();
                            },
                          );
                        },
                      ),
                      const SizedBox(height: 12),
                      _buildSettingItem(
                        icon: Icons.download_outlined,
                        title: 'التحميلات',
                        onTap: () {
                          // TODO: Navigate to downloads screen
                        },
                      ),
                      const SizedBox(height: 12),
                      _buildSettingItem(
                        icon: Icons.help_outline,
                        title: 'المساعدة والدعم',
                        onTap: () {
                          // TODO: Navigate to help screen
                        },
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Logout Section
                      _buildSettingItem(
                        icon: Icons.logout,
                        title: 'تسجيل الخروج',
                        onTap: _handleSignOut,
                        isDestructive: true,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: const Color(0xFF6366F1).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        title,
        style: GoogleFonts.cairo(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: const Color(0xFF6366F1),
        ),
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String title,
    required String value,
    Color? valueColor,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: themeProvider.isDarkMode
                ? const Color(0xFF1E293B)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: const Color(0xFF6366F1),
                  size: 20,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: themeProvider.isDarkMode
                            ? Colors.grey[400]
                            : Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: valueColor ??
                            (themeProvider.isDarkMode
                                ? Colors.white
                                : Colors.black87),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
