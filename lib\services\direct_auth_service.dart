import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import '../config/firebase_config.dart';

class DirectAuthService {
  // تهيئة Firebase مباشرة
  static Future<void> initializeFirebase() async {
    try {
      if (Firebase.apps.isEmpty) {
        // استخدام FirebaseConfig بدلاً من التهيئة المباشرة
        await FirebaseConfig.initialize();
        print('✅ Firebase تم تهيئته بنجاح');
      } else {
        print('✅ Firebase مُهيأ مسبقاً');
      }
    } catch (e) {
      print('❌ خطأ في تهيئة Firebase: $e');
      rethrow;
    }
  }

  // تسجيل الدخول كضيف مباشرة
  static Future<User?> signInAsGuestDirect() async {
    try {
      // تهيئة Firebase أولاً
      await initializeFirebase();

      print('🔄 بدء تسجيل الدخول كضيف...');

      // تسجيل الدخول كضيف
      final UserCredential userCredential =
          await FirebaseAuth.instance.signInAnonymously();

      if (userCredential.user != null) {
        print('✅ تم تسجيل الدخول كضيف بنجاح');
        print('UID: ${userCredential.user!.uid}');
        print('Anonymous: ${userCredential.user!.isAnonymous}');
        return userCredential.user;
      } else {
        print('❌ فشل في الحصول على بيانات المستخدم');
        return null;
      }
    } catch (e) {
      print('❌ خطأ في تسجيل الدخول كضيف: $e');
      print('نوع الخطأ: ${e.runtimeType}');
      if (e is FirebaseAuthException) {
        print('كود الخطأ: ${e.code}');
        print('رسالة الخطأ: ${e.message}');
      }
      rethrow;
    }
  }

  // التحقق من حالة المستخدم
  static User? getCurrentUser() {
    return FirebaseAuth.instance.currentUser;
  }

  // تسجيل الخروج
  static Future<void> signOut() async {
    try {
      await FirebaseAuth.instance.signOut();
      print('✅ تم تسجيل الخروج بنجاح');
    } catch (e) {
      print('❌ خطأ في تسجيل الخروج: $e');
      rethrow;
    }
  }

  // فحص حالة Firebase
  static Map<String, dynamic> getFirebaseStatus() {
    return {
      'isInitialized': Firebase.apps.isNotEmpty,
      'appsCount': Firebase.apps.length,
      'currentUser': FirebaseAuth.instance.currentUser?.uid,
      'isAnonymous': FirebaseAuth.instance.currentUser?.isAnonymous ?? false,
    };
  }
}
