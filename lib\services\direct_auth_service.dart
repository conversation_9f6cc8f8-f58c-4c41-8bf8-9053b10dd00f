import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

class DirectAuthService {
  // إعدادات Firebase مباشرة
  static const FirebaseOptions _webOptions = FirebaseOptions(
    apiKey: 'AIzaSyD__fwZYKuehthlJoqGKSDt0YN1TCsPUY8',
    appId: '1:801031214670:web:a179401f6b476d34db551f',
    messagingSenderId: '801031214670',
    projectId: 'legal2025',
    authDomain: 'legal2025.firebaseapp.com',
    storageBucket: 'legal2025.firebasestorage.app',
  );

  // تهيئة Firebase مباشرة
  static Future<void> initializeFirebase() async {
    try {
      if (Firebase.apps.isEmpty) {
        if (kIsWeb) {
          await Firebase.initializeApp(options: _webOptions);
        } else {
          await Firebase.initializeApp();
        }
        print('✅ Firebase تم تهيئته بنجاح');
      }
    } catch (e) {
      print('❌ خطأ في تهيئة Firebase: $e');
      rethrow;
    }
  }

  // تسجيل الدخول كضيف مباشرة
  static Future<User?> signInAsGuestDirect() async {
    try {
      // تهيئة Firebase أولاً
      await initializeFirebase();
      
      print('🔄 بدء تسجيل الدخول كضيف...');
      
      // تسجيل الدخول كضيف
      final UserCredential userCredential = await FirebaseAuth.instance.signInAnonymously();
      
      if (userCredential.user != null) {
        print('✅ تم تسجيل الدخول كضيف بنجاح');
        print('UID: ${userCredential.user!.uid}');
        print('Anonymous: ${userCredential.user!.isAnonymous}');
        return userCredential.user;
      } else {
        print('❌ فشل في الحصول على بيانات المستخدم');
        return null;
      }
    } catch (e) {
      print('❌ خطأ في تسجيل الدخول كضيف: $e');
      print('نوع الخطأ: ${e.runtimeType}');
      if (e is FirebaseAuthException) {
        print('كود الخطأ: ${e.code}');
        print('رسالة الخطأ: ${e.message}');
      }
      rethrow;
    }
  }

  // التحقق من حالة المستخدم
  static User? getCurrentUser() {
    return FirebaseAuth.instance.currentUser;
  }

  // تسجيل الخروج
  static Future<void> signOut() async {
    try {
      await FirebaseAuth.instance.signOut();
      print('✅ تم تسجيل الخروج بنجاح');
    } catch (e) {
      print('❌ خطأ في تسجيل الخروج: $e');
      rethrow;
    }
  }

  // فحص حالة Firebase
  static Map<String, dynamic> getFirebaseStatus() {
    return {
      'isInitialized': Firebase.apps.isNotEmpty,
      'appsCount': Firebase.apps.length,
      'currentUser': FirebaseAuth.instance.currentUser?.uid,
      'isAnonymous': FirebaseAuth.instance.currentUser?.isAnonymous ?? false,
    };
  }
}
