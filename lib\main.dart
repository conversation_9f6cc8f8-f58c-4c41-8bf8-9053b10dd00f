import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'package:provider/provider.dart';

import 'package:flutter_localizations/flutter_localizations.dart';
import 'models/subject.dart';
import 'data/academic_data.dart';
import 'data/chat_data.dart';
import 'screens/subjects_screen.dart';
import 'screens/chat_screen.dart';
import 'providers/theme_provider.dart';
import 'providers/simple_auth_provider.dart';
import 'widgets/auth/auth_wrapper.dart';
import 'providers/firebase_provider.dart';
import 'config/firebase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Firebase
  await FirebaseConfig.initialize();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
        ChangeNotifierProvider(create: (context) => FirebaseProvider()),
        ChangeNotifierProvider(create: (context) => SimpleAuthProvider()),
      ],
      child: const ShariaLawApp(),
    ),
  );
}

class ShariaLawApp extends StatelessWidget {
  const ShariaLawApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return MaterialApp(
          title: 'تطبيق الشريعة والقانون',
          debugShowCheckedModeBanner: false,
          theme: themeProvider.currentTheme.copyWith(
            textTheme: themeProvider.currentTheme.textTheme.apply(
              fontFamily: GoogleFonts.cairo().fontFamily,
            ),
          ),
          darkTheme: themeProvider.currentTheme.copyWith(
            textTheme: themeProvider.currentTheme.textTheme.apply(
              fontFamily: GoogleFonts.cairo().fontFamily,
            ),
          ),
          themeMode:
              themeProvider.isDarkMode ? ThemeMode.dark : ThemeMode.light,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar'), // العربية
          ],
          locale: const Locale('ar'),
          home: const AuthWrapper(),
          builder: (context, child) {
            // تحديث شريط الحالة بناءً على الثيم
            SystemChrome.setSystemUIOverlayStyle(
              SystemUiOverlayStyle(
                statusBarColor: Colors.transparent,
                statusBarIconBrightness:
                    themeProvider.isDarkMode
                        ? Brightness.light
                        : Brightness.dark,
                systemNavigationBarColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF0F172A)
                        : Colors.white,
                systemNavigationBarIconBrightness:
                    themeProvider.isDarkMode
                        ? Brightness.light
                        : Brightness.dark,
              ),
            );
            final direction = ui.TextDirection.rtl;
            return Directionality(textDirection: direction, child: child!);
          },
        );
      },
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CommunityScreen(),
    const ChatScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: _screens[_currentIndex],
          bottomNavigationBar: Container(
            height: 85,
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF1E293B)
                      : Colors.white,
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color:
                      themeProvider.isDarkMode
                          ? Colors.black.withValues(alpha: 0.3)
                          : Colors.black.withValues(alpha: 0.1),
                  blurRadius: 25,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 4),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(25),
              child: BottomNavigationBar(
                currentIndex: _currentIndex,
                onTap: (index) => setState(() => _currentIndex = index),
                backgroundColor: Colors.transparent,
                elevation: 0,
                selectedItemColor: const Color(0xFF6366F1),
                unselectedItemColor:
                    themeProvider.isDarkMode
                        ? const Color(0xFF64748B)
                        : Colors.grey[500],
                selectedLabelStyle: GoogleFonts.cairo(
                  fontSize: 11,
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFF6366F1),
                ),
                unselectedLabelStyle: GoogleFonts.cairo(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF64748B)
                          : Colors.grey[500],
                ),
                type: BottomNavigationBarType.fixed,
                items: [
                  BottomNavigationBarItem(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _currentIndex == 0
                                ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(Icons.home_rounded, size: 24),
                    ),
                    label: 'الرئيسية',
                  ),
                  BottomNavigationBarItem(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _currentIndex == 1
                                ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(Icons.groups_rounded, size: 24),
                    ),
                    label: 'المجتمع',
                  ),
                  BottomNavigationBarItem(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _currentIndex == 2
                                ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Stack(
                        children: [
                          const Icon(Icons.chat_bubble_rounded, size: 24),
                          if (ChatData.getTotalUnreadCount() > 0)
                            Positioned(
                              right: 0,
                              top: 0,
                              child: Container(
                                padding: const EdgeInsets.all(3),
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [
                                      Color(0xFFFF6B6B),
                                      Color(0xFFFF5252),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Colors.white,
                                    width: 1.5,
                                  ),
                                ),
                                constraints: const BoxConstraints(
                                  minWidth: 18,
                                  minHeight: 18,
                                ),
                                child: Text(
                                  '${ChatData.getTotalUnreadCount()}',
                                  style: GoogleFonts.cairo(
                                    color: Colors.white,
                                    fontSize: 9,
                                    fontWeight: FontWeight.w700,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    label: 'الدردشة',
                  ),
                  BottomNavigationBarItem(
                    icon: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color:
                            _currentIndex == 3
                                ? const Color(0xFF6366F1).withValues(alpha: 0.1)
                                : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(Icons.account_circle_rounded, size: 24),
                    ),
                    label: 'الملف الشخصي',
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

// Post Model
class Post {
  final String id;
  final String authorName;
  final String content;
  final DateTime timestamp;
  int likes;
  int comments;
  int shares;
  bool isLiked;
  final String? userRole;
  final bool isPinned;

  Post({
    required this.id,
    required this.authorName,
    required this.content,
    required this.timestamp,
    required this.likes,
    required this.comments,
    required this.shares,
    required this.isLiked,
    this.userRole,
    required this.isPinned,
  });
}

// Home Screen
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final List<AcademicYear> academicYears = AcademicData.getAcademicYears();

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Modern Hero Header
                SliverToBoxAdapter(
                  child: Container(
                    height: 220,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF667EEA),
                          const Color(0xFF764BA2),
                          const Color(0xFF6366F1),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(32),
                        bottomRight: Radius.circular(32),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Header Content
                        Positioned(
                          top: 20,
                          left: 20,
                          right: 20,
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.white.withValues(alpha: 0.15),
                              borderRadius: BorderRadius.circular(24),
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.2),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.1),
                                  blurRadius: 20,
                                  offset: const Offset(0, 8),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'أهلاً وسهلاً! 🌟',
                                        style: GoogleFonts.cairo(
                                          fontSize: 24,
                                          fontWeight: FontWeight.w800,
                                          color: Colors.white,
                                          height: 1.2,
                                        ),
                                      ),
                                      const SizedBox(height: 6),
                                      Text(
                                        'مرحباً بك في تطبيق كلية الشريعة والقانون',
                                        style: GoogleFonts.cairo(
                                          fontSize: 14,
                                          color: Colors.white.withValues(
                                            alpha: 0.9,
                                          ),
                                          height: 1.4,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Notification Button
                                Container(
                                  width: 45,
                                  height: 45,
                                  decoration: BoxDecoration(
                                    color: Colors.white.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(16),
                                    border: Border.all(
                                      color: Colors.white.withValues(
                                        alpha: 0.3,
                                      ),
                                      width: 1,
                                    ),
                                  ),
                                  child: IconButton(
                                    onPressed: () => _showNotifications(),
                                    icon: Stack(
                                      children: [
                                        const Icon(
                                          Icons.notifications_rounded,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                        Positioned(
                                          right: 2,
                                          top: 2,
                                          child: Container(
                                            width: 10,
                                            height: 10,
                                            decoration: BoxDecoration(
                                              color: const Color(0xFFFF4757),
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              border: Border.all(
                                                color: Colors.white,
                                                width: 1,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // Spacing
                const SliverToBoxAdapter(child: SizedBox(height: 16)),

                // Section Title
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'الفرق الدراسية',
                          style: GoogleFonts.cairo(
                            fontSize: 24,
                            fontWeight: FontWeight.w700,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFFF1F5F9)
                                    : const Color(0xFF1F2937),
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          'اختر الفرقة الدراسية للوصول إلى المواد والمحاضرات',
                          style: GoogleFonts.cairo(
                            fontSize: 16,
                            color:
                                themeProvider.isDarkMode
                                    ? const Color(0xFF94A3B8)
                                    : const Color(0xFF6B7280),
                          ),
                        ),
                        const SizedBox(height: 12),
                      ],
                    ),
                  ),
                ),

                // Academic Years List
                SliverPadding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final year = academicYears[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: _buildSimpleYearCard(year, index),
                      );
                    }, childCount: academicYears.length),
                  ),
                ),

                // Bottom spacing
                const SliverToBoxAdapter(child: SizedBox(height: 100)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSimpleYearCard(AcademicYear year, int index) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return GestureDetector(
          onTap: () => _navigateToYear(year),
          child: Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(int.parse(year.color.replaceFirst('#', '0xFF'))),
                  Color(
                    int.parse(year.color.replaceFirst('#', '0xFF')),
                  ).withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Color(
                    int.parse(year.color.replaceFirst('#', '0xFF')),
                  ).withValues(alpha: themeProvider.isDarkMode ? 0.4 : 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        year.arabicName,
                        style: GoogleFonts.cairo(
                          fontSize: 22,
                          fontWeight: FontWeight.w800,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'كلية الشريعة والقانون',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        '${year.semesters.length} فصول دراسية',
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: Colors.white.withValues(alpha: 0.8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_rounded,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.notifications, color: Color(0xFF6366F1)),
                SizedBox(width: 8),
                Text('الإشعارات'),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [Text('لا توجد إشعارات جديدة')],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  void _navigateToYear(AcademicYear year) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => SubjectsScreen(year: year)),
    );
  }
}

// Community Screen
class CommunityScreen extends StatefulWidget {
  const CommunityScreen({super.key});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen> {
  // متغيرات إنشاء المنشور
  // Post creation state
  bool _isCreatingPost = false;
  final TextEditingController _postController = TextEditingController();
  final FocusNode _postFocusNode = FocusNode();
  bool _isAnonymous = false;

  // Poll state
  bool _isPollMode = false;
  final List<TextEditingController> _pollControllers = [
    TextEditingController(),
    TextEditingController(),
  ];

  // Media handling
  final ImagePicker _imagePicker = ImagePicker();
  List<XFile> _selectedImages = [];
  PlatformFile? _selectedFile;

  // Media state
  bool _hasImage = false;
  bool _hasFile = false;

  // Comment controllers
  final Map<String, TextEditingController> _commentControllers = {};
  final Map<String, bool> _showComments = {};
  final Map<String, List<Map<String, dynamic>>> _postComments = {};

  // Poll voting state
  final Map<String, int?> _pollVotes = {};
  final Map<String, Map<int, int>> _pollResults = {};

  // Animation controllers for interactions
  final Map<String, bool> _likeAnimations = {};
  final Map<String, bool> _shareAnimations = {};

  @override
  void dispose() {
    _postController.dispose();
    _postFocusNode.dispose();
    for (var controller in _pollControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  final List<Post> posts = [
    Post(
      id: '1',
      authorName: 'أحمد محمد',
      content:
          'مرحباً بالجميع! هل يمكن أن يساعدني أحد في فهم موضوع أصول الفقه؟',
      timestamp: DateTime.now().subtract(const Duration(hours: 2)),
      likes: 15,
      comments: 8,
      shares: 3,
      isLiked: false,
      userRole: 'طالب',
      isPinned: false,
    ),
    Post(
      id: '2',
      authorName: 'د. فاطمة السيد',
      content:
          'تذكير: موعد تسليم بحث القانون المدني هو يوم الأحد القادم. لا تنسوا المراجع المطلوبة.',
      timestamp: DateTime.now().subtract(const Duration(hours: 5)),
      likes: 42,
      comments: 12,
      shares: 8,
      isLiked: true,
      userRole: 'أستاذ',
      isPinned: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                // Modern Header
                SliverToBoxAdapter(
                  child: Container(
                    height: 140,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF667EEA),
                          const Color(0xFF764BA2),
                          const Color(0xFF6366F1),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(24),
                        bottomRight: Radius.circular(24),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.fromLTRB(20, 20, 20, 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'مجتمع الطلاب',
                                      style: GoogleFonts.cairo(
                                        fontSize: 24,
                                        fontWeight: FontWeight.w800,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'شارك وتفاعل مع زملائك',
                                      style: GoogleFonts.cairo(
                                        fontSize: 14,
                                        color: Colors.white.withValues(
                                          alpha: 0.9,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // Notification Icon
                              Container(
                                width: 44,
                                height: 44,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: IconButton(
                                  onPressed: () => _showNotifications(),
                                  icon: Stack(
                                    children: [
                                      const Icon(
                                        Icons.notifications_rounded,
                                        color: Colors.white,
                                        size: 22,
                                      ),
                                      Positioned(
                                        right: 2,
                                        top: 2,
                                        child: Container(
                                          width: 8,
                                          height: 8,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFFFF4757),
                                            borderRadius: BorderRadius.circular(
                                              4,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Spacer(),
                          // Quick Stats
                          Row(
                            children: [
                              _buildQuickStat('125', 'طالب نشط'),
                              const SizedBox(width: 20),
                              _buildQuickStat('48', 'منشور اليوم'),
                              const SizedBox(width: 20),
                              _buildQuickStat('12', 'مناقشة'),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Create Post Section
                SliverToBoxAdapter(
                  child: Container(
                    margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF1E293B)
                              : Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color:
                              themeProvider.isDarkMode
                                  ? Colors.black.withValues(alpha: 0.3)
                                  : Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Modern Facebook-style Post Creator
                        _buildModernPostCreator(),
                      ],
                    ),
                  ),
                ),

                // Posts List
                SliverPadding(
                  padding: const EdgeInsets.all(20),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate((context, index) {
                      final post = posts[index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: _buildModernPostCard(post),
                      );
                    }, childCount: posts.length),
                  ),
                ),

                // Bottom spacing
                const SliverToBoxAdapter(child: SizedBox(height: 100)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildQuickStat(String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: GoogleFonts.cairo(
            fontSize: 16,
            fontWeight: FontWeight.w800,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.cairo(
            fontSize: 11,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildModernPostCard(Post post) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color:
                    themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Post Header
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Profile Picture
                    Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFF6366F1),
                            const Color(0xFF8B5CF6),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(22),
                      ),
                      child: Center(
                        child: Text(
                          post.authorName[0],
                          style: GoogleFonts.cairo(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Author Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                post.authorName,
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w700,
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFFF1F5F9)
                                          : const Color(0xFF1F2937),
                                ),
                              ),
                              const SizedBox(width: 6),
                              if (post.userRole == 'أستاذ')
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 6,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF10B981),
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: Text(
                                    'أستاذ',
                                    style: GoogleFonts.cairo(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              if (post.isPinned) ...[
                                const SizedBox(width: 6),
                                Icon(
                                  Icons.push_pin_rounded,
                                  size: 14,
                                  color: const Color(0xFFF59E0B),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 2),
                          Row(
                            children: [
                              Text(
                                _formatTimestamp(post.timestamp),
                                style: GoogleFonts.cairo(
                                  fontSize: 13,
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFF94A3B8)
                                          : const Color(0xFF6B7280),
                                ),
                              ),
                              const SizedBox(width: 4),
                              Icon(
                                Icons.public,
                                size: 12,
                                color:
                                    themeProvider.isDarkMode
                                        ? const Color(0xFF94A3B8)
                                        : const Color(0xFF6B7280),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    // More Options
                    IconButton(
                      onPressed: () => _showPostOptions(post),
                      icon: Icon(
                        Icons.more_horiz,
                        color: const Color(0xFF6B7280),
                      ),
                      iconSize: 20,
                    ),
                  ],
                ),
              ),

              // Post Content
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  post.content,
                  style: GoogleFonts.cairo(
                    fontSize: 15,
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFFF1F5F9)
                            : const Color(0xFF1F2937),
                    height: 1.5,
                  ),
                ),
              ),

              // Enhanced content display for polls and images
              if (post.content.contains('📊 استطلاع:') ||
                  post.content.contains('📷 صورة مرفقة')) ...[
                const SizedBox(height: 12),
                _buildPostExtras(post),
              ],

              const SizedBox(height: 12),

              // Engagement Stats
              if (post.likes > 0 || post.comments > 0)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    children: [
                      if (post.likes > 0) ...[
                        Row(
                          children: [
                            Container(
                              width: 20,
                              height: 20,
                              decoration: BoxDecoration(
                                color: const Color(0xFFEF4444),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Icon(
                                Icons.favorite,
                                color: Colors.white,
                                size: 12,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              '${post.likes}',
                              style: GoogleFonts.cairo(
                                fontSize: 13,
                                color: const Color(0xFF6B7280),
                              ),
                            ),
                          ],
                        ),
                      ],
                      const Spacer(),
                      if (post.comments > 0)
                        Text(
                          '${post.comments} تعليق',
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: const Color(0xFF6B7280),
                          ),
                        ),
                      if (post.shares > 0) ...[
                        const SizedBox(width: 8),
                        Text(
                          '${post.shares} مشاركة',
                          style: GoogleFonts.cairo(
                            fontSize: 13,
                            color: const Color(0xFF6B7280),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

              if (post.likes > 0 || post.comments > 0)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Divider(height: 1, color: const Color(0xFFE5E7EB)),
                ),

              // Action Buttons
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildPostActionButton(
                        icon:
                            post.isLiked
                                ? Icons.favorite
                                : Icons.favorite_border,
                        label: 'إعجاب',
                        color:
                            post.isLiked
                                ? const Color(0xFFEF4444)
                                : const Color(0xFF6B7280),
                        onTap: () => _toggleLikeWithAnimation(post),
                        postId: post.id,
                      ),
                    ),
                    Expanded(
                      child: _buildPostActionButton(
                        icon: Icons.chat_bubble_outline,
                        label: 'تعليق',
                        color: const Color(0xFF6B7280),
                        onTap: () => _toggleComments(post.id),
                        postId: post.id,
                      ),
                    ),
                    Expanded(
                      child: _buildPostActionButton(
                        icon: Icons.share_outlined,
                        label: 'مشاركة',
                        color: const Color(0xFF6B7280),
                        onTap: () => _sharePost(post),
                        postId: post.id,
                      ),
                    ),
                  ],
                ),
              ),

              // Comments Section
              if (_showComments[post.id] == true) ...[
                const SizedBox(height: 12),
                _buildCommentsSection(post.id),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildPostExtras(Post post) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Poll display
          if (post.content.contains('📊 استطلاع:')) ...[
            _buildPollDisplay(post),
            const SizedBox(height: 12),
          ],

          // Image display
          if (post.content.contains('📷 صورة مرفقة')) ...[_buildImageDisplay()],
        ],
      ),
    );
  }

  Widget _buildPollDisplay(Post post) {
    // استخراج خيارات الاستطلاع من النص
    final lines = post.content.split('\n');
    final pollOptions = <String>[];
    bool inPoll = false;

    for (final line in lines) {
      if (line.contains('📊 استطلاع:')) {
        inPoll = true;
        continue;
      }
      if (inPoll && line.startsWith('• ')) {
        pollOptions.add(line.substring(2));
      }
    }

    // تهيئة نتائج الاستطلاع إذا لم تكن موجودة
    if (!_pollResults.containsKey(post.id)) {
      _pollResults[post.id] = {};
      for (int i = 0; i < pollOptions.length; i++) {
        _pollResults[post.id]![i] =
            (i == 0)
                ? 15
                : (i == 1)
                ? 8
                : 3; // نتائج أولية متنوعة
      }
    }

    final results = _pollResults[post.id]!;
    final totalVotes = results.values.fold(0, (sum, votes) => sum + votes);
    final userVote = _pollVotes[post.id];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF8B5CF6).withValues(alpha: 0.05),
            const Color(0xFF3B82F6).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF8B5CF6).withValues(alpha: 0.2),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان الاستطلاع مع تأثيرات حديثة
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [const Color(0xFF8B5CF6), const Color(0xFF3B82F6)],
              ),
              borderRadius: BorderRadius.circular(25),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.poll_rounded, color: Colors.white, size: 20),
                const SizedBox(width: 8),
                Text(
                  'استطلاع تفاعلي',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),

          // خيارات الاستطلاع
          ...pollOptions.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final votes = results[index] ?? 0;
            final percentage = totalVotes > 0 ? (votes / totalVotes * 100) : 0;
            final isSelected = userVote == index;
            final hasVoted = userVote != null;

            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: hasVoted ? null : () => _voteInPoll(post.id, index),
                  borderRadius: BorderRadius.circular(16),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color:
                            isSelected
                                ? const Color(0xFF8B5CF6)
                                : const Color(0xFFE5E7EB),
                        width: isSelected ? 2 : 1,
                      ),
                      color:
                          isSelected
                              ? const Color(0xFF8B5CF6).withValues(alpha: 0.1)
                              : Colors.white,
                      boxShadow:
                          isSelected
                              ? [
                                BoxShadow(
                                  color: const Color(
                                    0xFF8B5CF6,
                                  ).withValues(alpha: 0.2),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ]
                              : [],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            // أيقونة التصويت
                            Container(
                              width: 24,
                              height: 24,
                              decoration: BoxDecoration(
                                color:
                                    isSelected
                                        ? const Color(0xFF8B5CF6)
                                        : Colors.transparent,
                                border: Border.all(
                                  color:
                                      isSelected
                                          ? const Color(0xFF8B5CF6)
                                          : const Color(0xFFD1D5DB),
                                  width: 2,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child:
                                  isSelected
                                      ? Icon(
                                        Icons.check,
                                        color: Colors.white,
                                        size: 16,
                                      )
                                      : null,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                option,
                                style: GoogleFonts.cairo(
                                  fontSize: 15,
                                  fontWeight:
                                      isSelected
                                          ? FontWeight.w600
                                          : FontWeight.w500,
                                  color:
                                      isSelected
                                          ? const Color(0xFF8B5CF6)
                                          : const Color(0xFF374151),
                                ),
                              ),
                            ),
                            if (hasVoted) ...[
                              Text(
                                '$votes',
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: const Color(0xFF6B7280),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${percentage.toStringAsFixed(1)}%',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  color: const Color(0xFF8B5CF6),
                                ),
                              ),
                            ],
                          ],
                        ),

                        // شريط التقدم
                        if (hasVoted) ...[
                          const SizedBox(height: 12),
                          Container(
                            height: 6,
                            decoration: BoxDecoration(
                              color: const Color(0xFFF3F4F6),
                              borderRadius: BorderRadius.circular(3),
                            ),
                            child: FractionallySizedBox(
                              alignment: Alignment.centerRight,
                              widthFactor: percentage / 100,
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors:
                                        isSelected
                                            ? [
                                              const Color(0xFF8B5CF6),
                                              const Color(0xFF3B82F6),
                                            ]
                                            : [
                                              const Color(0xFFD1D5DB),
                                              const Color(0xFF9CA3AF),
                                            ],
                                  ),
                                  borderRadius: BorderRadius.circular(3),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            );
          }),

          // إحصائيات الاستطلاع
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFF8FAFC),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFE5E7EB)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.how_to_vote_rounded,
                  size: 16,
                  color: const Color(0xFF6B7280),
                ),
                const SizedBox(width: 8),
                Text(
                  '$totalVotes صوت',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF6B7280),
                  ),
                ),
                const Spacer(),
                if (userVote == null)
                  Text(
                    'اضغط للتصويت',
                    style: GoogleFonts.cairo(
                      fontSize: 11,
                      color: const Color(0xFF8B5CF6),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageDisplay() {
    // عرض الصور الفعلية المرفقة
    if (_selectedImages.isNotEmpty) {
      return _buildRealImageDisplay(_selectedImages);
    }

    // عرض افتراضي للصور المحفوظة في المنشورات
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFF667EEA).withValues(alpha: 0.1),
            const Color(0xFF764BA2).withValues(alpha: 0.1),
          ],
        ),
        border: Border.all(
          color: const Color(0xFF667EEA).withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Stack(
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFF667EEA).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.image_rounded,
                    size: 48,
                    color: const Color(0xFF667EEA),
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  'صور مرفقة',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF667EEA),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'اضغط للعرض',
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: const Color(0xFF64748B),
                  ),
                ),
              ],
            ),
          ),
          // تأثير لمعان
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.1),
                    Colors.transparent,
                    Colors.white.withValues(alpha: 0.05),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRealImageDisplay(List<XFile> images) {
    if (images.length == 1) {
      return _buildSingleImageDisplay(images.first);
    } else {
      return _buildMultipleImagesDisplay(images);
    }
  }

  Widget _buildSingleImageDisplay(XFile image) {
    return Container(
      height: 300,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Image.network(
          image.path,
          fit: BoxFit.cover,
          width: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              decoration: BoxDecoration(
                color: const Color(0xFFF1F5F9),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image_rounded,
                      size: 48,
                      color: const Color(0xFF94A3B8),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'خطأ في تحميل الصورة',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        color: const Color(0xFF64748B),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildMultipleImagesDisplay(List<XFile> images) {
    return SizedBox(
      height: 250,
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: images.length == 2 ? 2 : 2,
          crossAxisSpacing: 8,
          mainAxisSpacing: 8,
          childAspectRatio: 1.2,
        ),
        itemCount: images.length > 4 ? 4 : images.length,
        itemBuilder: (context, index) {
          if (index == 3 && images.length > 4) {
            return _buildMoreImagesOverlay(images.length - 3);
          }
          return _buildImageTile(images[index]);
        },
      ),
    );
  }

  Widget _buildImageTile(XFile image) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Image.network(
          image.path,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: const Color(0xFFF1F5F9),
              child: Icon(
                Icons.broken_image_rounded,
                color: const Color(0xFF94A3B8),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildMoreImagesOverlay(int remainingCount) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        color: Colors.black.withValues(alpha: 0.7),
      ),
      child: Center(
        child: Text(
          '+$remainingCount',
          style: GoogleFonts.cairo(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildCommentsSection(String postId) {
    final comments = _postComments[postId] ?? [];

    // التأكد من وجود controller للمنشور
    if (!_commentControllers.containsKey(postId)) {
      _commentControllers[postId] = TextEditingController();
    }
    final controller = _commentControllers[postId]!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8FAFC),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE2E8F0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Comment Input
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: const Color(0xFF3B82F6),
                child: Text(
                  'أ',
                  style: GoogleFonts.cairo(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Consumer<ThemeProvider>(
                  builder: (context, themeProvider, child) {
                    return Container(
                      decoration: BoxDecoration(
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF334155)
                                : Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color:
                              themeProvider.isDarkMode
                                  ? const Color(0xFF475569)
                                  : const Color(0xFFE2E8F0),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: controller,
                              style: GoogleFonts.cairo(
                                fontSize: 14,
                                color:
                                    themeProvider.isDarkMode
                                        ? const Color(0xFFF1F5F9)
                                        : const Color(0xFF1F2937),
                              ),
                              decoration: InputDecoration(
                                hintText: 'اكتب تعليق <...',
                                hintStyle: GoogleFonts.cairo(
                                  color:
                                      themeProvider.isDarkMode
                                          ? const Color(0xFF94A3B8)
                                          : const Color(0xFF94A3B8),
                                  fontSize: 14,
                                ),
                                border: InputBorder.none,
                                contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                              onSubmitted:
                                  (value) => _addComment(postId, value),
                            ),
                          ),
                          IconButton(
                            onPressed:
                                () => _addComment(postId, controller.text),
                            icon: const Icon(
                              Icons.send,
                              color: Color(0xFF3B82F6),
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),

          // Comments List
          if (comments.isNotEmpty) ...[
            const SizedBox(height: 16),
            ...comments.map((comment) => _buildCommentItem(postId, comment)),
          ],
        ],
      ),
    );
  }

  Widget _buildCommentItem(String postId, Map<String, dynamic> comment) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CircleAvatar(
            radius: 14,
            backgroundColor: const Color(0xFF10B981),
            child: Text(
              comment['author'][0],
              style: GoogleFonts.cairo(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE2E8F0)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        comment['author'],
                        style: GoogleFonts.cairo(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1E293B),
                        ),
                      ),
                      const Spacer(),
                      if (comment['author'] == 'أنت')
                        GestureDetector(
                          onTap: () => _deleteComment(postId, comment['id']),
                          child: const Icon(
                            Icons.delete_outline,
                            size: 16,
                            color: Color(0xFFEF4444),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    comment['text'],
                    style: GoogleFonts.cairo(
                      fontSize: 14,
                      color: const Color(0xFF374151),
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      GestureDetector(
                        onTap: () => _toggleCommentLike(postId, comment['id']),
                        child: Row(
                          children: [
                            Icon(
                              comment['isLiked']
                                  ? Icons.favorite
                                  : Icons.favorite_border,
                              size: 16,
                              color:
                                  comment['isLiked']
                                      ? const Color(0xFFEF4444)
                                      : const Color(0xFF6B7280),
                            ),
                            if (comment['likes'] > 0) ...[
                              const SizedBox(width: 4),
                              Text(
                                '${comment['likes']}',
                                style: GoogleFonts.cairo(
                                  fontSize: 12,
                                  color: const Color(0xFF6B7280),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      const SizedBox(width: 16),
                      Text(
                        _formatTimestamp(comment['time']),
                        style: GoogleFonts.cairo(
                          fontSize: 12,
                          color: const Color(0xFF9CA3AF),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    String? postId,
  }) {
    final isActive =
        label == 'إعجاب' && postId != null && _likeAnimations[postId] == true;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          onTap();
          HapticFeedback.lightImpact();
        },
        borderRadius: BorderRadius.circular(16),
        splashColor: color.withValues(alpha: 0.2),
        highlightColor: color.withValues(alpha: 0.1),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            color: isActive ? color.withValues(alpha: 0.1) : Colors.transparent,
            border: Border.all(
              color:
                  isActive ? color.withValues(alpha: 0.3) : Colors.transparent,
              width: 1,
            ),
            boxShadow:
                isActive
                    ? [
                      BoxShadow(
                        color: color.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                    : [],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AnimatedScale(
                scale: isActive ? 1.2 : 1.0,
                duration: const Duration(milliseconds: 200),
                child: AnimatedRotation(
                  turns: isActive ? 0.1 : 0.0,
                  duration: const Duration(milliseconds: 200),
                  child: Icon(icon, color: color, size: 20),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  fontWeight: isActive ? FontWeight.w700 : FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  Widget _buildModernPostCreator() {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color:
                    themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.08),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              // Header Row
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: const Color(0xFF3B82F6),
                    child: Text(
                      'أ',
                      style: GoogleFonts.cairo(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _isCreatingPost = true;
                        });
                        _postFocusNode.requestFocus();
                      },
                      child: Consumer<ThemeProvider>(
                        builder: (context, themeProvider, child) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              color:
                                  themeProvider.isDarkMode
                                      ? const Color(0xFF334155)
                                      : const Color(0xFFF3F4F6),
                              borderRadius: BorderRadius.circular(25),
                              border: Border.all(
                                color:
                                    _isCreatingPost
                                        ? const Color(0xFF3B82F6)
                                        : themeProvider.isDarkMode
                                        ? const Color(0xFF475569)
                                        : const Color(0xFFE5E7EB),
                                width: _isCreatingPost ? 2 : 1,
                              ),
                            ),
                            child: Text(
                              'ما الذي تفكر فيه؟',
                              style: GoogleFonts.cairo(
                                color:
                                    themeProvider.isDarkMode
                                        ? const Color(0xFF94A3B8)
                                        : const Color(0xFF9CA3AF),
                                fontSize: 16,
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),

              // Expanded Content
              AnimatedSize(
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
                child:
                    _isCreatingPost
                        ? _buildExpandedPostContent()
                        : const SizedBox.shrink(),
              ),

              // Action Buttons Row
              if (!_isCreatingPost) ...[
                const SizedBox(height: 12),
                const Divider(height: 1, color: Color(0xFFE5E7EB)),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildQuickActionButton(
                        icon: Icons.photo_library,
                        label: 'صورة',
                        color: const Color(0xFF10B981),
                        isActive: _hasImage,
                        onTap: _toggleImageMode,
                      ),
                    ),
                    Expanded(
                      child: _buildQuickActionButton(
                        icon: Icons.poll,
                        label: 'استطلاع',
                        color: const Color(0xFF8B5CF6),
                        isActive: _isPollMode,
                        onTap: _togglePollMode,
                      ),
                    ),
                    Expanded(
                      child: _buildQuickActionButton(
                        icon: Icons.attach_file,
                        label: 'ملف',
                        color: const Color(0xFFEF4444),
                        isActive: _hasFile,
                        onTap: _toggleFileMode,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildExpandedPostContent() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        children: [
          // Text Input Area
          Consumer<ThemeProvider>(
            builder: (context, themeProvider, child) {
              return Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      themeProvider.isDarkMode
                          ? const Color(0xFF334155)
                          : const Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFF475569)
                            : const Color(0xFFE5E7EB),
                  ),
                ),
                child: TextField(
                  controller: _postController,
                  focusNode: _postFocusNode,
                  maxLines: null,
                  minLines: 3,
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    height: 1.5,
                    color:
                        themeProvider.isDarkMode
                            ? const Color(0xFFF1F5F9)
                            : const Color(0xFF1F2937),
                  ),
                  decoration: InputDecoration(
                    hintText: 'شاركنا أفكارك وآرائك...',
                    hintStyle: GoogleFonts.cairo(
                      fontSize: 16,
                      color:
                          themeProvider.isDarkMode
                              ? const Color(0xFF94A3B8)
                              : const Color(0xFF9CA3AF),
                    ),
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.zero,
                  ),
                  onChanged: (value) => setState(() {}),
                ),
              );
            },
          ),

          // Media Attachments
          if (_hasImage) _buildImageAttachment(),
          if (_hasFile) _buildFileAttachment(),
          if (_isPollMode) _buildPollCreation(),

          const SizedBox(height: 16),

          // Bottom Controls
          Row(
            children: [
              // Anonymous Toggle
              _buildAnonymousToggle(),

              const Spacer(),

              // Action Buttons
              TextButton(
                onPressed: _cancelPost,
                child: Text(
                  'إلغاء',
                  style: GoogleFonts.cairo(
                    color: const Color(0xFF6B7280),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              const SizedBox(width: 8),

              ElevatedButton(
                onPressed: _canPublishPost() ? _publishPost : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3B82F6),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'نشر',
                  style: GoogleFonts.cairo(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isActive = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
          decoration: BoxDecoration(
            color: isActive ? color.withValues(alpha: 0.1) : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border:
                isActive
                    ? Border.all(color: color.withValues(alpha: 0.3))
                    : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isActive ? color : const Color(0xFF6B7280),
                size: 20,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: GoogleFonts.cairo(
                  fontSize: 14,
                  color: isActive ? color : const Color(0xFF6B7280),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _toggleImageMode() async {
    setState(() {
      _isCreatingPost = true;
      _isPollMode = false; // إلغاء الاستطلاع عند اختيار صورة
    });

    if (!_hasImage) {
      await _selectImages();
    } else {
      setState(() {
        _hasImage = false;
        _selectedImages.clear();
      });
    }
    _postFocusNode.requestFocus();
  }

  Future<void> _selectImages() async {
    try {
      final List<XFile> images = await _imagePicker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        setState(() {
          _selectedImages = images;
          _hasImage = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في اختيار الصور: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFFEF4444),
          ),
        );
      }
    }
  }

  void _togglePollMode() {
    setState(() {
      _isCreatingPost = true;
      _isPollMode = !_isPollMode;
      if (_isPollMode) {
        _hasImage = false;
        _hasFile = false;
        _selectedImages.clear();
        _selectedFile = null;
        // التأكد من وجود خيارين على الأقل
        while (_pollControllers.length < 2) {
          _pollControllers.add(TextEditingController());
        }
      }
    });
    _postFocusNode.requestFocus();
  }

  Future<void> _toggleFileMode() async {
    setState(() {
      _isCreatingPost = true;
      _isPollMode = false;
    });

    if (!_hasFile) {
      await _selectFile();
    } else {
      setState(() {
        _hasFile = false;
        _selectedFile = null;
      });
    }
    _postFocusNode.requestFocus();
  }

  Future<void> _selectFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf',
          'doc',
          'docx',
          'xls',
          'xlsx',
          'ppt',
          'pptx',
          'txt',
        ],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _selectedFile = result.files.first;
          _hasFile = true;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'خطأ في اختيار الملف: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: const Color(0xFFEF4444),
          ),
        );
      }
    }
  }

  void _cancelPost() {
    setState(() {
      _isCreatingPost = false;
      _isPollMode = false;
      _hasImage = false;
      _hasFile = false;
      _isAnonymous = false;
      _selectedImages.clear();
      _selectedFile = null;
      _postController.clear();
      for (var controller in _pollControllers) {
        controller.clear();
      }
    });
  }

  Widget _buildImageAttachment() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      constraints: const BoxConstraints(maxHeight: 200),
      decoration: BoxDecoration(
        color: const Color(0xFFF0FDF4),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF10B981).withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child:
          _selectedImages.isNotEmpty
              ? _buildImageGrid()
              : _buildImagePlaceholder(),
    );
  }

  Widget _buildImageGrid() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(14),
      child: Stack(
        children: [
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: const EdgeInsets.all(8),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: _selectedImages.length == 1 ? 1 : 2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1.2,
            ),
            itemCount: _selectedImages.length > 4 ? 4 : _selectedImages.length,
            itemBuilder: (context, index) {
              final image = _selectedImages[index];
              return Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(image.path), // For web
                        fit: BoxFit.cover,
                        onError: (error, stackTrace) {
                          // Fallback for web
                        },
                      ),
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withValues(alpha: 0.1),
                          ],
                        ),
                      ),
                    ),
                  ),
                  if (index == 3 && _selectedImages.length > 4)
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: Colors.black.withValues(alpha: 0.6),
                      ),
                      child: Center(
                        child: Text(
                          '+${_selectedImages.length - 3}',
                          style: GoogleFonts.cairo(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap:
                  () => setState(() {
                    _hasImage = false;
                    _selectedImages.clear();
                  }),
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(Icons.close, size: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagePlaceholder() {
    return SizedBox(
      height: 120,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.image, size: 32, color: const Color(0xFF10B981)),
            const SizedBox(height: 4),
            Text(
              'صور مرفقة',
              style: GoogleFonts.cairo(
                fontSize: 12,
                color: const Color(0xFF10B981),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPollCreation() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [const Color(0xFFF8FAFF), const Color(0xFFF3F4F6)],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF8B5CF6).withValues(alpha: 0.2),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.poll_rounded,
                  color: const Color(0xFF8B5CF6),
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إنشاء استطلاع تفاعلي',
                      style: GoogleFonts.cairo(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                    ),
                    Text(
                      'اطرح سؤال < واحصل على آراء المجتمع',
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: const Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () => setState(() => _isPollMode = false),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.close_rounded,
                    size: 18,
                    color: const Color(0xFFEF4444),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Poll Options
          ...List.generate(_pollControllers.length, (index) {
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF8B5CF6),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color:
                              _pollControllers[index].text.isNotEmpty
                                  ? const Color(0xFF8B5CF6)
                                  : const Color(0xFFE5E7EB),
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: TextField(
                        controller: _pollControllers[index],
                        decoration: InputDecoration(
                          hintText: 'اكتب الخيار ${index + 1}...',
                          hintStyle: GoogleFonts.cairo(
                            color: const Color(0xFF9CA3AF),
                            fontSize: 14,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 12,
                          ),
                        ),
                        style: GoogleFonts.cairo(
                          fontSize: 14,
                          color: const Color(0xFF1F2937),
                        ),
                        onChanged: (value) => setState(() {}),
                      ),
                    ),
                  ),
                  if (_pollControllers.length > 2 && index >= 2) ...[
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: () => _removePollOption(index),
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.remove_rounded,
                          color: const Color(0xFFEF4444),
                          size: 18,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          }),

          // Add Option Button
          if (_pollControllers.length < 5)
            GestureDetector(
              onTap: _addPollOption,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                    style: BorderStyle.solid,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_rounded,
                      color: const Color(0xFF8B5CF6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'إضافة خيار جديد',
                      style: GoogleFonts.cairo(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF8B5CF6),
                      ),
                    ),
                  ],
                ),
              ),
            ),

          const SizedBox(height: 12),

          // Poll Info
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: const Color(0xFF3B82F6),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'سيتمكن الأعضاء من التصويت مرة واحدة فقط',
                    style: GoogleFonts.cairo(
                      fontSize: 12,
                      color: const Color(0xFF3B82F6),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnonymousToggle() {
    return GestureDetector(
      onTap: () => setState(() => _isAnonymous = !_isAnonymous),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color:
              _isAnonymous
                  ? const Color(0xFF3B82F6).withValues(alpha: 0.1)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                _isAnonymous
                    ? const Color(0xFF3B82F6)
                    : const Color(0xFFE5E7EB),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _isAnonymous
                  ? Icons.visibility_off
                  : Icons.visibility_off_outlined,
              size: 16,
              color:
                  _isAnonymous
                      ? const Color(0xFF3B82F6)
                      : const Color(0xFF6B7280),
            ),
            const SizedBox(width: 6),
            Text(
              'مجهول',
              style: GoogleFonts.cairo(
                fontSize: 13,
                color:
                    _isAnonymous
                        ? const Color(0xFF3B82F6)
                        : const Color(0xFF6B7280),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addPollOption() {
    if (_pollControllers.length < 5) {
      setState(() {
        _pollControllers.add(TextEditingController());
      });
    }
  }

  void _removePollOption(int index) {
    if (_pollControllers.length > 2 && index >= 2) {
      setState(() {
        _pollControllers[index].dispose();
        _pollControllers.removeAt(index);
      });
    }
  }

  Widget _buildFileAttachment() {
    final fileColor = _getFileColor(_selectedFile?.extension ?? '');

    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            fileColor.withValues(alpha: 0.05),
            fileColor.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: fileColor.withValues(alpha: 0.3), width: 2),
        boxShadow: [
          BoxShadow(
            color: fileColor.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة الملف مع تأثيرات حديثة
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [fileColor, fileColor.withValues(alpha: 0.8)],
              ),
              borderRadius: BorderRadius.circular(18),
              boxShadow: [
                BoxShadow(
                  color: fileColor.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              _getFileIcon(_selectedFile?.extension ?? ''),
              size: 28,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 16),

          // معلومات الملف
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedFile?.name ?? 'ملف مرفق',
                  style: GoogleFonts.cairo(
                    fontSize: 16,
                    color: const Color(0xFF1F2937),
                    fontWeight: FontWeight.w700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 6),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: fileColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        (_selectedFile?.extension ?? '').toUpperCase(),
                        style: GoogleFonts.cairo(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          color: fileColor,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _formatFileSize(_selectedFile?.size ?? 0),
                      style: GoogleFonts.cairo(
                        fontSize: 12,
                        color: const Color(0xFF6B7280),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // أزرار التحكم
          Column(
            children: [
              // زر التحميل/الفتح
              GestureDetector(
                onTap: () => _openFile(_selectedFile!),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: fileColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.open_in_new_rounded,
                    size: 20,
                    color: fileColor,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              // زر الحذف
              GestureDetector(
                onTap:
                    () => setState(() {
                      _hasFile = false;
                      _selectedFile = null;
                    }),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFFEF4444).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.close_rounded,
                    size: 20,
                    color: Color(0xFFEF4444),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getFileColor(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return const Color(0xFFEF4444);
      case 'doc':
      case 'docx':
        return const Color(0xFF2563EB);
      case 'xls':
      case 'xlsx':
        return const Color(0xFF059669);
      case 'ppt':
      case 'pptx':
        return const Color(0xFFD97706);
      case 'txt':
        return const Color(0xFF6B7280);
      default:
        return const Color(0xFF8B5CF6);
    }
  }

  void _openFile(PlatformFile file) {
    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                Text(
                  'خيارات الملف',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 20),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildFileOption(
                      icon: Icons.visibility_rounded,
                      label: 'عرض',
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pop(context);
                        _showSuccessMessage('تم فتح الملف للعرض');
                      },
                    ),
                    _buildFileOption(
                      icon: Icons.download_rounded,
                      label: 'تحميل',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        _showSuccessMessage('تم بدء التحميل');
                      },
                    ),
                    _buildFileOption(
                      icon: Icons.share_rounded,
                      label: 'مشاركة',
                      color: const Color(0xFF8B5CF6),
                      onTap: () {
                        Navigator.pop(context);
                        _showSuccessMessage('تم فتح خيارات المشاركة');
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  Widget _buildFileOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF6B7280),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'txt':
        return Icons.text_snippet;
      default:
        return Icons.attach_file;
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  void _publishPost() {
    _publishInlinePost();
  }

  bool _canPublishPost() {
    // يمكن النشر إذا كان هناك نص أو صورة أو ملف أو استطلاع مكتمل
    if (_postController.text.trim().isNotEmpty) return true;
    if (_hasImage) return true;
    if (_hasFile) return true;
    if (_isPollMode) {
      // التحقق من وجود خيارين على الأقل مع محتوى
      int validOptions = 0;
      for (var controller in _pollControllers) {
        if (controller.text.trim().isNotEmpty) {
          validOptions++;
        }
      }
      return validOptions >= 2;
    }
    return false;
  }

  void _publishInlinePost() {
    if (!_canPublishPost()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('يرجى إضافة محتوى للمنشور', style: GoogleFonts.cairo()),
          backgroundColor: const Color(0xFFEF4444),
        ),
      );
      return;
    }

    String content = _postController.text.trim();

    // إضافة محتوى الاستطلاع إذا كان موجود
    if (_isPollMode) {
      final validOptions = <String>[];
      for (var controller in _pollControllers) {
        final option = controller.text.trim();
        if (option.isNotEmpty) {
          validOptions.add(option);
        }
      }
      if (validOptions.length >= 2) {
        if (content.isNotEmpty) content += '\n\n';
        content += '📊 استطلاع:\n';
        for (int i = 0; i < validOptions.length; i++) {
          content += '• ${validOptions[i]}\n';
        }
      }
    }

    // إضافة إشارة للملف إذا كان موجود
    if (_hasFile && _selectedFile != null) {
      if (content.isNotEmpty) content += '\n\n';
      content += '📎 ملف مرفق: ${_selectedFile!.name}';
    }

    // إضافة إشارة للصور إذا كانت موجودة
    if (_hasImage && _selectedImages.isNotEmpty) {
      if (content.isNotEmpty) content += '\n\n';
      content += '📷 ${_selectedImages.length} صورة مرفقة';
    }

    // التأكد من وجود محتوى
    if (content.isEmpty) {
      content = 'منشور جديد';
    }

    // إنشاء منشور جديد
    final newPost = Post(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      authorName: _isAnonymous ? 'مجهول' : 'أحمد محمد علي',
      content: content,
      timestamp: DateTime.now(),
      likes: 0,
      comments: 0,
      shares: 0,
      isLiked: false,
      isPinned: false,
    );

    setState(() {
      posts.insert(0, newPost); // إضافة في المقدمة
      _isCreatingPost = false;
      _isPollMode = false;
      _isAnonymous = false;
      _hasImage = false;
      _hasFile = false;
      _selectedImages.clear();
      _selectedFile = null;
      _postController.clear();
      for (var controller in _pollControllers) {
        controller.clear();
      }
    });

    // إظهار رسالة نجاح محسنة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text('تم نشر المنشور بنجاح! 🎉', style: GoogleFonts.cairo()),
          ],
        ),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showNotifications() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(
              'الإشعارات',
              style: GoogleFonts.cairo(fontWeight: FontWeight.w700),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildNotificationItem(
                  'أحمد علي',
                  'أعجب بمنشورك',
                  '5 دقائق',
                  Icons.favorite,
                  const Color(0xFFEF4444),
                ),
                _buildNotificationItem(
                  'فاطمة محمد',
                  'علقت على منشورك',
                  '10 دقائق',
                  Icons.chat_bubble,
                  const Color(0xFF6366F1),
                ),
                _buildNotificationItem(
                  'د. محمد أحمد',
                  'نشر منشوراً جديداً',
                  '30 دقيقة',
                  Icons.post_add,
                  const Color(0xFF10B981),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق', style: GoogleFonts.cairo()),
              ),
            ],
          ),
    );
  }

  Widget _buildNotificationItem(
    String name,
    String action,
    String time,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: name,
                        style: GoogleFonts.cairo(
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF1F2937),
                        ),
                      ),
                      TextSpan(
                        text: ' $action',
                        style: GoogleFonts.cairo(
                          color: const Color(0xFF6B7280),
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  time,
                  style: GoogleFonts.cairo(
                    fontSize: 12,
                    color: const Color(0xFF9CA3AF),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showPostOptions(Post post) {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildOptionItem(Icons.bookmark_border, 'حفظ المنشور'),
                _buildOptionItem(Icons.link, 'نسخ الرابط'),
                _buildOptionItem(Icons.report_outlined, 'الإبلاغ عن المنشور'),
                if (post.authorName == 'أحمد محمد علي') ...[
                  const Divider(),
                  _buildOptionItem(Icons.edit, 'تعديل المنشور'),
                  _buildOptionItem(
                    Icons.delete_outline,
                    'حذف المنشور',
                    isDestructive: true,
                  ),
                ],
              ],
            ),
          ),
    );
  }

  Widget _buildOptionItem(
    IconData icon,
    String label, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : const Color(0xFF6B7280),
      ),
      title: Text(
        label,
        style: GoogleFonts.cairo(
          color: isDestructive ? Colors.red : const Color(0xFF1F2937),
        ),
      ),
      onTap: () => Navigator.pop(context),
    );
  }

  void _voteInPoll(String postId, int optionIndex) {
    setState(() {
      // تسجيل صوت المستخدم
      _pollVotes[postId] = optionIndex;

      // تحديث النتائج
      if (_pollResults[postId] != null) {
        _pollResults[postId]![optionIndex] =
            (_pollResults[postId]![optionIndex] ?? 0) + 1;
      }
    });

    // تأثير صوتي وبصري
    HapticFeedback.lightImpact();

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            const SizedBox(width: 8),
            Text(
              'تم تسجيل صوتك بنجاح!',
              style: GoogleFonts.cairo(color: Colors.white),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _toggleLikeWithAnimation(Post post) {
    setState(() {
      post.isLiked = !post.isLiked;
      post.likes += post.isLiked ? 1 : -1;
    });

    // تأثير بصري وصوتي
    if (post.isLiked) {
      // محاكاة صوت الفقعة
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.favorite, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              Text('تم الإعجاب! 💖', style: GoogleFonts.cairo()),
            ],
          ),
          backgroundColor: const Color(0xFFEF4444),
          duration: const Duration(milliseconds: 800),
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.only(bottom: 100, left: 20, right: 20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  void _toggleComments(String postId) {
    setState(() {
      _showComments[postId] = !(_showComments[postId] ?? false);
      if (!_commentControllers.containsKey(postId)) {
        _commentControllers[postId] = TextEditingController();
      }
      if (!_postComments.containsKey(postId)) {
        _postComments[postId] = [];
      }
    });
  }

  void _addComment(String postId, String comment) {
    if (comment.trim().isEmpty) return;

    setState(() {
      _postComments[postId]?.add({
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'text': comment.trim(),
        'author': 'أنت',
        'time': DateTime.now(),
        'likes': 0,
        'isLiked': false,
      });
      _commentControllers[postId]?.clear();
    });
  }

  void _deleteComment(String postId, String commentId) {
    setState(() {
      _postComments[postId]?.removeWhere(
        (comment) => comment['id'] == commentId,
      );
    });
  }

  void _toggleCommentLike(String postId, String commentId) {
    setState(() {
      final comments = _postComments[postId];
      if (comments != null) {
        final commentIndex = comments.indexWhere((c) => c['id'] == commentId);
        if (commentIndex != -1) {
          comments[commentIndex]['isLiked'] =
              !comments[commentIndex]['isLiked'];
          comments[commentIndex]['likes'] +=
              comments[commentIndex]['isLiked'] ? 1 : -1;
        }
      }
    });
  }

  void _sharePost(Post post) {
    setState(() {
      post.shares += 1;
      _shareAnimations[post.id] = true;
    });

    // تأثير اهتزاز
    HapticFeedback.mediumImpact();

    // إظهار خيارات المشاركة مع تصميم حديث
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(25),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, -5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // مؤشر السحب
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE5E7EB),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                Text(
                  'مشاركة المنشور',
                  style: GoogleFonts.cairo(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: const Color(0xFF1F2937),
                  ),
                ),
                const SizedBox(height: 20),

                // خيارات المشاركة
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildShareOption(
                      icon: Icons.copy_rounded,
                      label: 'نسخ الرابط',
                      color: const Color(0xFF3B82F6),
                      onTap: () {
                        Navigator.pop(context);
                        _showSuccessMessage('تم نسخ الرابط');
                      },
                    ),
                    _buildShareOption(
                      icon: Icons.share_rounded,
                      label: 'مشاركة خارجية',
                      color: const Color(0xFF10B981),
                      onTap: () {
                        Navigator.pop(context);
                        _showSuccessMessage('تم فتح خيارات المشاركة');
                      },
                    ),
                    _buildShareOption(
                      icon: Icons.bookmark_add_rounded,
                      label: 'حفظ',
                      color: const Color(0xFF8B5CF6),
                      onTap: () {
                        Navigator.pop(context);
                        _showSuccessMessage('تم حفظ المنشور');
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
    );

    // إعادة تعيين الأنيميشن
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _shareAnimations[post.id] = false;
        });
      }
    });
  }

  Widget _buildShareOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: color.withValues(alpha: 0.3), width: 2),
            ),
            child: Icon(icon, color: color, size: 28),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: GoogleFonts.cairo(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF6B7280),
            ),
          ),
        ],
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle_rounded, color: Colors.white),
            const SizedBox(width: 8),
            Text(message, style: GoogleFonts.cairo(color: Colors.white)),
          ],
        ),
        backgroundColor: const Color(0xFF10B981),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

// Profile Screen
class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    final authProvider = Provider.of<SimpleAuthProvider>(
      context,
      listen: false,
    );
    final user = authProvider.firebaseUser;

    if (user != null) {
      _nameController.text = user.displayName ?? '';
      _emailController.text = user.email ?? '';
    }
  }

  // دالة تعديل الملف الشخصي
  Future<void> _showEditProfileDialog() async {
    final authProvider = Provider.of<SimpleAuthProvider>(
      context,
      listen: false,
    );
    final user = authProvider.firebaseUser;

    if (user?.isAnonymous == true) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'لا يمكن تعديل ملف الضيف. قم بإنشاء حساب أولاً',
            style: GoogleFonts.cairo(),
          ),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    _loadUserData();

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'تعديل الملف الشخصي',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'الاسم الكامل',
                  labelStyle: GoogleFonts.cairo(),
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.person),
                ),
                style: GoogleFonts.cairo(),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  labelStyle: GoogleFonts.cairo(),
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.email),
                ),
                style: GoogleFonts.cairo(),
                enabled: false, // الإيميل لا يمكن تعديله
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(color: Colors.grey[600]),
              ),
            ),
            TextButton(
              onPressed: () async {
                await _updateProfile();
                Navigator.of(context).pop();
              },
              child: Text(
                'حفظ',
                style: GoogleFonts.cairo(color: const Color(0xFF6366F1)),
              ),
            ),
          ],
        );
      },
    );
  }

  // دالة تحديث الملف الشخصي
  Future<void> _updateProfile() async {
    final authProvider = Provider.of<SimpleAuthProvider>(
      context,
      listen: false,
    );
    final user = authProvider.firebaseUser;

    if (user == null) return;

    try {
      // تحديث الاسم
      if (_nameController.text.trim().isNotEmpty) {
        await user.updateDisplayName(_nameController.text.trim());
        await user.reload();

        // إعادة تحميل البيانات
        setState(() {});

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '✅ تم تحديث الملف الشخصي بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '❌ فشل في تحديث الملف الشخصي: $e',
              style: GoogleFonts.cairo(),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // دالة تسجيل الخروج
  Future<void> _handleSignOut() async {
    // عرض dialog للتأكيد
    final bool? shouldSignOut = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'تسجيل الخروج',
            style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'هل أنت متأكد من أنك تريد تسجيل الخروج؟',
            style: GoogleFonts.cairo(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(
                'إلغاء',
                style: GoogleFonts.cairo(color: Colors.grey[600]),
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(
                'تسجيل الخروج',
                style: GoogleFonts.cairo(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );

    if (shouldSignOut == true && mounted) {
      try {
        // استخدام SimpleAuthProvider لتسجيل الخروج
        final authProvider = Provider.of<SimpleAuthProvider>(
          context,
          listen: false,
        );
        final success = await authProvider.signOut();

        if (success && mounted) {
          // عرض رسالة نجاح
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'تم تسجيل الخروج بنجاح',
                style: GoogleFonts.cairo(),
              ),
              backgroundColor: Colors.green,
            ),
          );

          // AuthWrapper سيتولى التنقل تلقائياً لصفحة تسجيل الدخول
        } else {
          // عرض رسالة خطأ
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  authProvider.error ?? 'فشل في تسجيل الخروج',
                  style: GoogleFonts.cairo(),
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        // عرض رسالة خطأ
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ: $e', style: GoogleFonts.cairo()),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<ThemeProvider, SimpleAuthProvider>(
      builder: (context, themeProvider, authProvider, child) {
        final user = authProvider.firebaseUser;
        return Scaffold(
          backgroundColor:
              themeProvider.isDarkMode
                  ? const Color(0xFF0F172A)
                  : const Color(0xFFF8FAFC),
          body: SafeArea(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // Profile Header
                  Container(
                    height: 280,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF667EEA),
                          const Color(0xFF764BA2),
                          const Color(0xFF6366F1),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(32),
                        bottomRight: Radius.circular(32),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Background Pattern
                        Positioned.fill(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(32),
                                bottomRight: Radius.circular(32),
                              ),
                            ),
                          ),
                        ),
                        // Profile Content
                        Positioned(
                          top: 40,
                          left: 24,
                          right: 24,
                          child: Column(
                            children: [
                              // Profile Avatar
                              Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.15),
                                  borderRadius: BorderRadius.circular(60),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 3,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.1,
                                      ),
                                      blurRadius: 20,
                                      offset: const Offset(0, 8),
                                    ),
                                  ],
                                ),
                                child: Icon(
                                  Icons.person_rounded,
                                  size: 60,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 20),
                              // Name
                              Text(
                                user?.displayName ?? 'مستخدم',
                                style: GoogleFonts.cairo(
                                  fontSize: 28,
                                  fontWeight: FontWeight.w800,
                                  color: Colors.white,
                                ),
                              ),
                              const SizedBox(height: 8),
                              // Email
                              if (user?.email != null) ...[
                                Text(
                                  user!.email!,
                                  style: GoogleFonts.cairo(
                                    fontSize: 16,
                                    color: Colors.white.withValues(alpha: 0.9),
                                  ),
                                ),
                                const SizedBox(height: 8),
                              ],
                              // Role
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(20),
                                  border: Border.all(
                                    color: Colors.white.withValues(alpha: 0.3),
                                    width: 1,
                                  ),
                                ),
                                child: Text(
                                  user?.isAnonymous == true
                                      ? 'ضيف'
                                      : 'طالب مسجل',
                                  style: GoogleFonts.cairo(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 12),
                              // University
                              Text(
                                'كلية الشريعة والقانون',
                                style: GoogleFonts.cairo(
                                  fontSize: 16,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30),
                  // Settings
                  Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        _buildSettingItem(
                          icon: Icons.edit,
                          title: 'تعديل الملف الشخصي',
                          onTap: _showEditProfileDialog,
                        ),
                        _buildSettingItem(
                          icon: Icons.download,
                          title: 'التحميلات',
                          onTap: () {},
                        ),
                        Consumer<ThemeProvider>(
                          builder: (context, themeProvider, child) {
                            return _buildSettingItem(
                              icon:
                                  themeProvider.isDarkMode
                                      ? Icons.dark_mode
                                      : Icons.light_mode,
                              title: 'الوضع المظلم',
                              trailing: Switch(
                                value: themeProvider.isDarkMode,
                                onChanged: (value) {
                                  themeProvider.toggleTheme();
                                },
                                activeColor: const Color(0xFF6366F1),
                              ),
                              onTap: () {
                                themeProvider.toggleTheme();
                              },
                            );
                          },
                        ),
                        _buildSettingItem(
                          icon: Icons.logout,
                          title: 'تسجيل الخروج',
                          onTap: _handleSignOut,
                          isDestructive: true,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Widget? trailing,
    bool isDestructive = false,
  }) {
    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color:
                themeProvider.isDarkMode
                    ? const Color(0xFF1E293B)
                    : Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color:
                    (themeProvider.isDarkMode
                        ? Colors.black.withValues(alpha: 0.3)
                        : Colors.black.withValues(alpha: 0.08)),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
            border: Border.all(
              color:
                  themeProvider.isDarkMode
                      ? const Color(0xFF334155).withValues(alpha: 0.5)
                      : const Color(0xFFE5E7EB).withValues(alpha: 0.5),
              width: 1,
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // Icon Container
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: (isDestructive
                                ? Colors.red
                                : const Color(0xFF6366F1))
                            .withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        icon,
                        color:
                            isDestructive
                                ? Colors.red
                                : const Color(0xFF6366F1),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Title
                    Expanded(
                      child: Text(
                        title,
                        style: GoogleFonts.cairo(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color:
                              isDestructive
                                  ? Colors.red
                                  : (themeProvider.isDarkMode
                                      ? const Color(0xFFF1F5F9)
                                      : const Color(0xFF1F2937)),
                        ),
                      ),
                    ),
                    // Trailing
                    if (trailing != null) ...[
                      trailing,
                    ] else ...[
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 16,
                        color:
                            themeProvider.isDarkMode
                                ? const Color(0xFF64748B)
                                : const Color(0xFF9CA3AF),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
