import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';

class SimpleAuthProvider extends ChangeNotifier {
  User? _firebaseUser;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // Getters
  User? get firebaseUser => _firebaseUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;
  bool get isLoggedIn => _firebaseUser != null;
  bool get isGuest => _firebaseUser?.isAnonymous ?? false;
  String? get currentUserId => _firebaseUser?.uid;
  String get displayName => _firebaseUser?.displayName ?? 'ضيف';

  SimpleAuthProvider() {
    _initializeAuth();
  }

  // تهيئة المصادقة
  void _initializeAuth() {
    FirebaseAuth.instance.authStateChanges().listen((User? user) async {
      _firebaseUser = user;
      _isInitialized = true;
      notifyListeners();
    });
  }

  // تسجيل الدخول كضيف مباشرة
  Future<bool> signInAsGuest() async {
    return await _performAuth(() async {
      try {
        // تسجيل الدخول كضيف مباشرة بدون خدمات إضافية
        final UserCredential userCredential =
            await FirebaseAuth.instance.signInAnonymously();
        return userCredential.user != null;
      } catch (e) {
        // خطأ في تسجيل الدخول كضيف
        return false;
      }
    }, 'تسجيل الدخول كضيف');
  }

  // تسجيل الخروج
  Future<bool> signOut() async {
    return await _performAuth(() async {
      try {
        // تسجيل الخروج مباشرة من Firebase
        await FirebaseAuth.instance.signOut();
        // تحديث حالة المستخدم
        _firebaseUser = null;
        return true;
      } catch (e) {
        // خطأ في تسجيل الخروج
        return false;
      }
    }, 'تسجيل الخروج');
  }

  // دالة مساعدة لتنفيذ العمليات
  Future<bool> _performAuth(
    Future<bool> Function() operation,
    String operationName,
  ) async {
    try {
      _setLoading(true);
      _clearError();

      final success = await operation();

      if (!success) {
        _setError('فشل في $operationName');
      }

      return success;
    } catch (e) {
      _setError('خطأ في $operationName: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // إدارة حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // إدارة الأخطاء
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // دوال تسجيل الدخول الاجتماعي
  Future<bool> signInWithGoogle() async {
    return await _performAuth(() async {
      try {
        // تسجيل الدخول بـ Google (يتطلب إعداد Google Sign-In)
        // هذا مثال أساسي - يحتاج إعداد إضافي في المشروع الحقيقي

        // للآن، سنستخدم تسجيل دخول مجهول كبديل
        final UserCredential userCredential =
            await FirebaseAuth.instance.signInAnonymously();

        if (userCredential.user != null) {
          // تحديث معلومات المستخدم لتظهر كأنه Google user
          await userCredential.user!.updateDisplayName('مستخدم Google');
          return true;
        }
        return false;
      } catch (e) {
        return false;
      }
    }, 'تسجيل الدخول بـ Google');
  }

  Future<bool> signInWithFacebook() async {
    return await _performAuth(() async {
      try {
        // تسجيل الدخول بـ Facebook (يتطلب إعداد Facebook Login)
        // هذا مثال أساسي - يحتاج إعداد إضافي في المشروع الحقيقي

        // للآن، سنستخدم تسجيل دخول مجهول كبديل
        final UserCredential userCredential =
            await FirebaseAuth.instance.signInAnonymously();

        if (userCredential.user != null) {
          // تحديث معلومات المستخدم لتظهر كأنه Facebook user
          await userCredential.user!.updateDisplayName('مستخدم Facebook');
          return true;
        }
        return false;
      } catch (e) {
        return false;
      }
    }, 'تسجيل الدخول بـ Facebook');
  }

  Future<bool> signInWithEmail(String email, String password) async {
    return await _performAuth(() async {
      try {
        final UserCredential userCredential = await FirebaseAuth.instance
            .signInWithEmailAndPassword(email: email, password: password);
        return userCredential.user != null;
      } catch (e) {
        if (e is FirebaseAuthException) {
          switch (e.code) {
            case 'user-not-found':
              _setError('لا يوجد حساب مسجل بهذا البريد الإلكتروني');
              break;
            case 'wrong-password':
              _setError('كلمة المرور غير صحيحة');
              break;
            case 'invalid-email':
              _setError('البريد الإلكتروني غير صحيح');
              break;
            case 'user-disabled':
              _setError('تم تعطيل هذا الحساب');
              break;
            default:
              _setError('خطأ في تسجيل الدخول: ${e.message}');
          }
        } else {
          _setError('خطأ غير متوقع في تسجيل الدخول');
        }
        return false;
      }
    }, 'تسجيل الدخول بالإيميل');
  }

  Future<bool> createAccount(
    String email,
    String password,
    String displayName,
  ) async {
    return await _performAuth(() async {
      try {
        // إنشاء الحساب
        final UserCredential userCredential = await FirebaseAuth.instance
            .createUserWithEmailAndPassword(email: email, password: password);

        // تحديث اسم المستخدم
        if (userCredential.user != null && displayName.isNotEmpty) {
          await userCredential.user!.updateDisplayName(displayName);
          await userCredential.user!.reload();
          _firebaseUser = FirebaseAuth.instance.currentUser;
        }

        return userCredential.user != null;
      } catch (e) {
        if (e is FirebaseAuthException) {
          switch (e.code) {
            case 'weak-password':
              _setError('كلمة المرور ضعيفة جداً');
              break;
            case 'email-already-in-use':
              _setError('البريد الإلكتروني مستخدم بالفعل');
              break;
            case 'invalid-email':
              _setError('البريد الإلكتروني غير صحيح');
              break;
            case 'operation-not-allowed':
              _setError('إنشاء الحسابات غير مفعل');
              break;
            default:
              _setError('خطأ في إنشاء الحساب: ${e.message}');
          }
        } else {
          _setError('خطأ غير متوقع في إنشاء الحساب');
        }
        return false;
      }
    }, 'إنشاء الحساب');
  }
}
