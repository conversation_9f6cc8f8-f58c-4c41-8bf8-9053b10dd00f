import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/direct_auth_service.dart';

class SimpleAuthProvider extends ChangeNotifier {
  User? _firebaseUser;
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  // Getters
  User? get firebaseUser => _firebaseUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;
  bool get isLoggedIn => _firebaseUser != null;
  bool get isGuest => _firebaseUser?.isAnonymous ?? false;
  String? get currentUserId => _firebaseUser?.uid;
  String get displayName => _firebaseUser?.displayName ?? 'ضيف';

  SimpleAuthProvider() {
    _initializeAuth();
  }

  // تهيئة المصادقة
  void _initializeAuth() {
    FirebaseAuth.instance.authStateChanges().listen((User? user) async {
      _firebaseUser = user;
      _isInitialized = true;
      notifyListeners();
    });
  }

  // تسجيل الدخول كضيف مباشرة
  Future<bool> signInAsGuest() async {
    return await _performAuth(() async {
      final user = await DirectAuthService.signInAsGuestDirect();
      return user != null;
    }, 'تسجيل الدخول كضيف');
  }

  // تسجيل الخروج
  Future<bool> signOut() async {
    return await _performAuth(() async {
      await DirectAuthService.signOut();
      return true;
    }, 'تسجيل الخروج');
  }

  // دالة مساعدة لتنفيذ العمليات
  Future<bool> _performAuth(Future<bool> Function() operation, String operationName) async {
    try {
      _setLoading(true);
      _clearError();
      
      final success = await operation();
      
      if (!success) {
        _setError('فشل في $operationName');
      }
      
      return success;
    } catch (e) {
      _setError('خطأ في $operationName: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // إدارة حالة التحميل
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // إدارة الأخطاء
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // دوال مؤقتة للتوافق مع الكود الموجود
  Future<bool> signInWithGoogle() async {
    _setError('تسجيل الدخول بـ Google غير متاح حالياً');
    return false;
  }

  Future<bool> signInWithApple() async {
    _setError('تسجيل الدخول بـ Apple غير متاح حالياً');
    return false;
  }

  Future<bool> signInWithEmail(String email, String password) async {
    _setError('تسجيل الدخول بالإيميل غير متاح حالياً');
    return false;
  }

  Future<bool> createAccount(String email, String password, String displayName) async {
    _setError('إنشاء الحساب غير متاح حالياً');
    return false;
  }
}
